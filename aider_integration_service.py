#!/usr/bin/env python

import os
import sys
import json
import time
import inspect
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Any
from collections import Counter, defaultdict
import re

# Add the aider-main directory to the Python path
sys.path.insert(0, os.path.abspath("aider-main"))

# Import directly from the dependency analysis output
DEPENDENCY_ANALYSIS_FILE = "aider_repo_map_output/dependency_analysis.txt"

# Try to import RepoMap directly
try:
    from aider.repomap import RepoMap
    from aider.io import InputOutput
    from aider.models import Model
    REPOMAP_AVAILABLE = True
except ImportError:
    print("Warning: Could not import RepoMap from aider. Dynamic analysis will be disabled.")
    REPOMAP_AVAILABLE = False


class AiderProjectManager:
    """
    Manages Aider projects and provides access to the RepoMap functionality.
    """

    def __init__(self, cache_ttl: int = 3600):
        """
        Initialize the project manager.

        Args:
            cache_ttl: Time-to-live for cache entries in seconds (default: 1 hour)
        """
        self.dependency_data = None
        self.class_inheritance_cache = {}  # (project_path, class_name) -> inheritance_dict
        self.symbol_cache = {}  # (project_path, file_path) -> symbols_dict
        self.cache_ttl = cache_ttl
        self.cache_timestamps = {}  # Cache key -> timestamp
        self.repo_maps = {}  # project_path -> RepoMap instance
        self.root = os.getcwd()  # Default root directory

        # Load static dependency data
        self._load_dependency_data()

    def _load_dependency_data(self):
        """
        Load dependency data from the analysis file.
        """
        try:
            if not os.path.exists(DEPENDENCY_ANALYSIS_FILE):
                print(f"Dependency analysis file not found: {DEPENDENCY_ANALYSIS_FILE}")
                return

            # Parse the dependency analysis file
            with open(DEPENDENCY_ANALYSIS_FILE, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract central files
            central_files = []
            central_files_section = content.split("## Top Central Files")[1].split("##")[0]
            for line in central_files_section.strip().split('\n'):
                if line.startswith('- '):
                    parts = line[2:].split(': referenced by ')
                    if len(parts) == 2:
                        file_path = parts[0]
                        ref_count = int(parts[1].split(' ')[0])
                        central_files.append({"file": file_path, "references": ref_count})

            # Extract strong dependencies
            dependencies = {}
            strong_deps_section = content.split("## Strong Dependencies")[1].split("##")[0]
            current_file = None

            for line in strong_deps_section.strip().split('\n'):
                if line.startswith('### '):
                    current_file = line[4:].split(' strongly depends on:')[0]
                    dependencies[current_file] = []
                elif line.startswith('- ') and current_file:
                    parts = line[2:].split(' (')
                    if len(parts) == 2:
                        dep_file = parts[0]
                        ref_count = int(parts[1].split(' references')[0])
                        dependencies[current_file].append((dep_file, ref_count))

            # Extract class dependencies
            class_dependencies = {}
            class_deps_section = content.split("## Class Dependencies")[1]
            current_class = None

            for line in class_deps_section.strip().split('\n'):
                if line.startswith('### '):
                    current_class = line[4:].split(' depends on:')[0]
                    class_dependencies[current_class] = []
                elif line.startswith('- ') and current_class:
                    parts = line[2:].split(' (')
                    if len(parts) == 2:
                        dep_class = parts[0]
                        ref_count = int(parts[1].split(' references')[0])
                        class_dependencies[current_class].append((dep_class, ref_count))

            self.dependency_data = {
                "central_files": central_files,
                "dependencies": dependencies,
                "class_dependencies": class_dependencies
            }
        except Exception as e:
            print(f"Error loading dependency data: {e}")
            self.dependency_data = None

    def _get_repo_map(self, project_path: str, model_name: str = "gpt-3.5-turbo") -> Optional[Any]:
        """
        Get or create a RepoMap instance for the given project.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use

        Returns:
            RepoMap instance or None if RepoMap is not available
        """
        if not REPOMAP_AVAILABLE:
            return None

        # Normalize the project path
        project_path = self._normalize_path(project_path)

        # Check if we already have a RepoMap for this project
        if project_path in self.repo_maps:
            return self.repo_maps[project_path]

        try:
            # Create a simple model instance for token counting
            model = Model(model_name)

            # Create an InputOutput instance
            io = InputOutput()

            # Initialize the RepoMap with a higher token limit for better coverage
            repo_map = RepoMap(
                map_tokens=8192,  # Increased token limit for better coverage
                root=project_path,
                main_model=model,
                io=io,
                repo_content_prefix="# Repository Map\n\nThis map shows the structure and dependencies of the codebase:\n\n",
                verbose=False
            )

            self.repo_maps[project_path] = repo_map
            return repo_map
        except Exception as e:
            print(f"Error creating RepoMap: {e}")
            return None

    def _normalize_path(self, path: str) -> str:
        """
        Normalize a file path for cross-platform consistency.

        Args:
            path: The file path to normalize

        Returns:
            Normalized path
        """
        if not path:
            return path

        # Handle relative paths
        if not os.path.isabs(path):
            # If the path is relative to the project root, make it absolute
            if os.path.exists(os.path.join(self.root, path)):
                path = os.path.join(self.root, path)
            # Otherwise, try to resolve it relative to the current directory
            else:
                path = os.path.abspath(path)

        # Convert to Path object and resolve
        try:
            normalized = Path(path).resolve()

            # Check if the path exists
            if not normalized.exists() and os.path.exists(path):
                # If the resolved path doesn't exist but the original does,
                # use the original path
                normalized = Path(path)
        except Exception:
            # If resolution fails, use the original path
            normalized = Path(path)

        # Convert back to string with forward slashes for cross-platform consistency
        return str(normalized).replace('\\', '/')

    def _is_cache_valid(self, cache_key: Tuple) -> bool:
        """
        Check if a cache entry is still valid based on TTL.

        Args:
            cache_key: The cache key to check

        Returns:
            True if the cache entry is valid, False otherwise
        """
        if cache_key not in self.cache_timestamps:
            return False

        timestamp = self.cache_timestamps[cache_key]
        return (time.time() - timestamp) < self.cache_ttl

    def _update_cache_timestamp(self, cache_key: Tuple) -> None:
        """
        Update the timestamp for a cache entry.

        Args:
            cache_key: The cache key to update
        """
        self.cache_timestamps[cache_key] = time.time()

    def _extract_class_info_from_repomap(self, project_path: str, model_name: str) -> Dict[str, Dict]:
        """
        Extract class inheritance information from RepoMap.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use

        Returns:
            Dictionary mapping class names to their inheritance information
        """
        repo_map = self._get_repo_map(project_path, model_name)
        if not repo_map:
            return {}

        class_info = {}

        # Get all Python files in the project
        python_files = []
        for root, _, files in os.walk(project_path):
            # Skip __pycache__ directories and other non-source directories
            if "__pycache__" in root or ".git" in root:
                continue

            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))

        # Extract class definitions and inheritance from each file
        for file_path in python_files:
            try:
                rel_path = os.path.relpath(file_path, project_path)
                abs_path = os.path.abspath(file_path)

                # Read the file content to analyze class definitions more accurately
                with open(abs_path, 'r', encoding='utf-8') as f:
                    file_content = f.read()

                # Get tags for the file
                tags = list(repo_map.get_tags(file_path, rel_path))

                # Find class definitions
                class_defs = []
                for tag in tags:
                    if tag.kind == 'def' and tag.name.startswith('class '):
                        class_defs.append(tag)
                    elif hasattr(tag, 'kind') and tag.kind == 'class':
                        class_defs.append(tag)

                # If no class definitions found through tags, try regex pattern matching
                if not class_defs:
                    class_pattern = r'class\s+(\w+)\s*(?:\((.*?)\))?:'
                    for match in re.finditer(class_pattern, file_content):
                        class_name = match.group(1)
                        base_classes_str = match.group(2) or ""

                        # Initialize class info
                        if class_name not in class_info:
                            class_info[class_name] = {
                                "file_path": rel_path,
                                "base_classes": [],
                                "derived_classes": [],
                                "methods": [],
                                "attributes": []
                            }

                        # Extract base classes
                        if base_classes_str:
                            # Handle complex inheritance with commas inside parentheses
                            # For example: class A(B, C(D, E), F):
                            base_classes = []
                            current = ""
                            paren_level = 0

                            for char in base_classes_str:
                                if char == '(' and paren_level == 0:
                                    paren_level += 1
                                    current += char
                                elif char == ')' and paren_level > 0:
                                    paren_level -= 1
                                    current += char
                                elif char == ',' and paren_level == 0:
                                    base_classes.append(current.strip())
                                    current = ""
                                else:
                                    current += char

                            if current:
                                base_classes.append(current.strip())

                            # Clean up base class names (remove generic parameters, etc.)
                            cleaned_bases = []
                            for base in base_classes:
                                # Extract just the class name without generic parameters
                                base_name = re.match(r'([a-zA-Z0-9_]+)', base)
                                if base_name:
                                    cleaned_bases.append(base_name.group(1))

                            class_info[class_name]["base_classes"] = cleaned_bases
                else:
                    # Process class definitions found through tags
                    for tag in class_defs:
                        class_name = tag.name
                        if hasattr(tag, 'name') and tag.name.startswith('class '):
                            class_name = tag.name.split(' ')[1]

                        # Initialize class info
                        if class_name not in class_info:
                            class_info[class_name] = {
                                "file_path": rel_path,
                                "base_classes": [],
                                "derived_classes": [],
                                "methods": [],
                                "attributes": []
                            }

                        # Look for inheritance information in the tag scope or file content
                        class_pattern = r'class\s+' + re.escape(class_name) + r'\s*\((.*?)\):'
                        match = re.search(class_pattern, file_content)
                        if match:
                            base_classes_str = match.group(1)
                            base_classes = [bc.strip().split('[')[0].split('(')[0] for bc in base_classes_str.split(',')]
                            class_info[class_name]["base_classes"] = base_classes

                # Extract methods for each class
                method_pattern = r'def\s+(\w+)\s*\('
                class_blocks = re.finditer(r'class\s+(\w+)[^\{]*?:\s*(?:\n\s+[^\n]+)*', file_content, re.MULTILINE)

                for class_block_match in class_blocks:
                    class_name = class_block_match.group(1)
                    if class_name in class_info:
                        class_block = class_block_match.group(0)
                        # Find methods in the class block
                        for method_match in re.finditer(method_pattern, class_block):
                            method_name = method_match.group(1)
                            if method_name not in ['__init__', '__str__', '__repr__']:  # Skip common magic methods
                                if method_name not in class_info[class_name]["methods"]:
                                    class_info[class_name]["methods"].append(method_name)
            except Exception as e:
                print(f"Error processing file {file_path}: {e}")
                continue

        # Build derived classes relationships
        for class_name, info in class_info.items():
            for base_class in info["base_classes"]:
                if base_class in class_info:
                    if class_name not in class_info[base_class]["derived_classes"]:
                        class_info[base_class]["derived_classes"].append(class_name)

        return class_info

    def get_top_central_files(self, project_path: str, model_name: str, count: int = 10) -> List[Dict]:
        """
        Get the top central files in the project based on reference count.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use
            count: Number of top files to return

        Returns:
            A list of dictionaries with file paths and reference counts
        """
        if not self.dependency_data:
            return []

        # Get the top central files from the dependency data
        central_files = self.dependency_data["central_files"]
        return central_files[:count]

    def get_files_that_import(self, project_path: str, model_name: str, target_file_path: str) -> List[str]:
        """
        Get a list of files that import (reference) the target file.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use
            target_file_path: Path to the target file

        Returns:
            A list of file paths that import the target file
        """
        # Try to use RepoMap for more accurate results
        repo_map = self._get_repo_map(project_path, model_name)
        if repo_map:
            try:
                # Normalize the target file path
                target_file_path = self._normalize_path(target_file_path)
                rel_path = os.path.relpath(target_file_path, project_path)

                # Get all Python files in the project
                python_files = []
                for root, _, files in os.walk(project_path):
                    for file in files:
                        if file.endswith('.py'):
                            python_files.append(os.path.join(root, file))

                # Find files that import the target file
                importing_files = []

                # Get tags for the target file to find defined symbols
                target_tags = list(repo_map.get_tags(target_file_path, rel_path))
                defined_symbols = [tag.name for tag in target_tags if tag.kind == 'def' or tag.kind == 'class']

                # Check each file for references to the defined symbols
                for file_path in python_files:
                    if file_path == target_file_path:
                        continue

                    file_rel_path = os.path.relpath(file_path, project_path)
                    file_tags = list(repo_map.get_tags(file_path, file_rel_path))

                    # Check if any of the defined symbols are referenced in this file
                    for tag in file_tags:
                        if tag.kind == 'ref' and tag.name in defined_symbols:
                            importing_files.append(file_rel_path)
                            break

                return importing_files
            except Exception as e:
                print(f"Error using RepoMap for get_files_that_import: {e}")
                # Fall back to static analysis

        # Fall back to static analysis if RepoMap is not available or fails
        if not self.dependency_data:
            return []

        # Get the dependencies from the dependency data
        dependencies = self.dependency_data["dependencies"]

        # Find files that import the target file
        importing_files = []
        target_file_rel = os.path.basename(target_file_path)

        for file, deps in dependencies.items():
            for dep_file, _ in deps:
                if dep_file == target_file_rel or dep_file.endswith('/' + target_file_rel):
                    importing_files.append(file)

        return importing_files

    def get_files_imported_by(self, project_path: str, model_name: str, target_file_path: str) -> List[str]:
        """
        Get a list of files that are imported by the target file.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use
            target_file_path: Path to the target file

        Returns:
            A list of file paths that are imported by the target file
        """
        # Try to use RepoMap for more accurate results
        repo_map = self._get_repo_map(project_path, model_name)
        if repo_map:
            try:
                # Normalize the target file path
                target_file_path = self._normalize_path(target_file_path)
                rel_path = os.path.relpath(target_file_path, project_path)

                # Get tags for the target file
                target_tags = list(repo_map.get_tags(target_file_path, rel_path))

                # Find references to other files
                imported_files = []

                # Look for reference tags in the target file
                for tag in target_tags:
                    if tag.kind == 'ref':
                        # Try to find the file that defines this symbol
                        defining_file = self._find_file_defining_symbol(project_path, model_name, tag.name)
                        if defining_file and defining_file != rel_path:
                            imported_files.append(defining_file)

                return list(set(imported_files))  # Remove duplicates
            except Exception as e:
                print(f"Error using RepoMap for get_files_imported_by: {e}")
                # Fall back to static analysis

        # Fall back to static analysis if RepoMap is not available or fails
        if not self.dependency_data:
            return []

        # Get the dependencies from the dependency data
        dependencies = self.dependency_data["dependencies"]

        # Find files imported by the target file
        target_file_rel = os.path.basename(target_file_path)

        # Look for the target file in the dependencies
        for file in dependencies:
            if file == target_file_rel or file.endswith('/' + target_file_rel):
                return [dep_file for dep_file, _ in dependencies[file]]

        return []

    def _find_file_defining_symbol(self, project_path: str, model_name: str, symbol_name: str) -> Optional[str]:
        """
        Find the file that defines a specific symbol.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use
            symbol_name: Name of the symbol to find

        Returns:
            Relative path to the file defining the symbol, or None if not found
        """
        # Check if we have this information in the cache
        cache_key = (project_path, symbol_name)
        if cache_key in self.symbol_cache and self._is_cache_valid(cache_key):
            return self.symbol_cache[cache_key]

        repo_map = self._get_repo_map(project_path, model_name)
        if not repo_map:
            return None

        # Get all Python files in the project
        python_files = []
        for root, _, files in os.walk(project_path):
            for file in files:
                if file.endswith('.py'):
                    python_files.append(os.path.join(root, file))

        # Look for the symbol definition in each file
        for file_path in python_files:
            rel_path = os.path.relpath(file_path, project_path)

            # Get tags for the file
            tags = list(repo_map.get_tags(file_path, rel_path))

            # Check if the symbol is defined in this file
            for tag in tags:
                if (tag.kind == 'def' or tag.kind == 'class') and tag.name == symbol_name:
                    # Update the cache
                    self.symbol_cache[cache_key] = rel_path
                    self._update_cache_timestamp(cache_key)
                    return rel_path

        return None

    def get_symbols_defined_in_file(self, project_path: str, model_name: str, file_path: str) -> Dict[str, List[str]]:
        """
        Get all symbols (functions, classes, variables) defined in a file.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use
            file_path: Path to the file to analyze

        Returns:
            Dictionary with symbol types as keys and lists of symbol names as values
        """
        # Check if we have this information in the cache
        cache_key = (project_path, file_path, 'symbols')
        if cache_key in self.symbol_cache and self._is_cache_valid(cache_key):
            return self.symbol_cache[cache_key]

        repo_map = self._get_repo_map(project_path, model_name)
        if not repo_map:
            return {'functions': [], 'classes': [], 'variables': []}

        try:
            # Normalize the file path
            file_path = self._normalize_path(file_path)
            rel_path = os.path.relpath(file_path, project_path)

            # Get tags for the file
            tags = list(repo_map.get_tags(file_path, rel_path))

            # Categorize symbols by type
            symbols = {
                'functions': [],
                'classes': [],
                'variables': [],
                'imports': []
            }

            for tag in tags:
                if tag.kind == 'def':
                    if tag.name.startswith('class '):
                        class_name = tag.name.split(' ')[1]
                        if class_name not in symbols['classes']:
                            symbols['classes'].append(class_name)
                    elif tag.name.startswith('def '):
                        func_name = tag.name.split(' ')[1]
                        if func_name not in symbols['functions']:
                            symbols['functions'].append(func_name)
                    else:
                        if tag.name not in symbols['functions']:
                            symbols['functions'].append(tag.name)
                elif tag.kind == 'class':
                    if tag.name not in symbols['classes']:
                        symbols['classes'].append(tag.name)
                elif tag.kind == 'variable':
                    if tag.name not in symbols['variables']:
                        symbols['variables'].append(tag.name)
                elif tag.kind == 'import':
                    if tag.name not in symbols['imports']:
                        symbols['imports'].append(tag.name)

            # If we have access to the file, try to extract more detailed information
            try:
                abs_path = os.path.abspath(file_path)
                if os.path.exists(abs_path):
                    with open(abs_path, 'r', encoding='utf-8') as f:
                        file_content = f.read()

                    # Extract imports
                    import_pattern = r'^(?:from\s+(\S+)\s+import\s+(.+)|import\s+(.+))$'
                    for line in file_content.split('\n'):
                        match = re.match(import_pattern, line.strip())
                        if match:
                            if match.group(1) and match.group(2):  # from X import Y
                                module = match.group(1)
                                imports = [imp.strip() for imp in match.group(2).split(',')]
                                for imp in imports:
                                    import_str = f"{module}.{imp}"
                                    if import_str not in symbols['imports']:
                                        symbols['imports'].append(import_str)
                            elif match.group(3):  # import X
                                imports = [imp.strip() for imp in match.group(3).split(',')]
                                for imp in imports:
                                    if imp not in symbols['imports']:
                                        symbols['imports'].append(imp)
            except Exception as e:
                print(f"Error extracting additional symbols from file: {e}")

            # Update the cache
            self.symbol_cache[cache_key] = symbols
            self._update_cache_timestamp(cache_key)

            return symbols
        except Exception as e:
            print(f"Error getting symbols from file: {e}")
            return {'functions': [], 'classes': [], 'variables': [], 'imports': []}

    def get_base_classes_of(self, project_path: str, model_name: str, class_name: str, file_path: str = None) -> List[Dict]:
        """
        Get the base classes of the specified class.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use
            class_name: Name of the class to analyze
            file_path: Path to the file containing the class (optional)

        Returns:
            A list of dictionaries with base class information
        """
        # Check if we have this information in the cache
        cache_key = (project_path, class_name, 'base_classes')
        if cache_key in self.class_inheritance_cache and self._is_cache_valid(cache_key):
            return self.class_inheritance_cache[cache_key]

        # Try to use dynamic analysis with RepoMap
        if REPOMAP_AVAILABLE:
            try:
                # Get class inheritance information from RepoMap
                class_info = self._extract_class_info_from_repomap(project_path, model_name)

                if class_name in class_info:
                    base_classes = []
                    for base_class_name in class_info[class_name]["base_classes"]:
                        if base_class_name in class_info:
                            base_classes.append({
                                "class_name": base_class_name,
                                "file_path": os.path.join(project_path, class_info[base_class_name]["file_path"]),
                                "module_path": self._get_module_path(class_info[base_class_name]["file_path"])
                            })

                    # Update the cache
                    self.class_inheritance_cache[cache_key] = base_classes
                    self._update_cache_timestamp(cache_key)

                    return base_classes
            except Exception as e:
                print(f"Error using RepoMap for class inheritance analysis: {e}")
                # Fall back to static mapping

        # Fall back to static mapping if RepoMap is not available or fails
        # Example class inheritance for Aider's codebase
        class_inheritance = {
            "Coder": {
                "base_classes": [],
                "file_path": "aider/coders/base_coder.py"
            },
            "EditBlockCoder": {
                "base_classes": ["Coder"],
                "file_path": "aider/coders/editblock_coder.py"
            },
            "WholeFileCoder": {
                "base_classes": ["Coder"],
                "file_path": "aider/coders/wholefile_coder.py"
            },
            "UDiffCoder": {
                "base_classes": ["Coder"],
                "file_path": "aider/coders/udiff_coder.py"
            },
            "PatchCoder": {
                "base_classes": ["Coder"],
                "file_path": "aider/coders/patch_coder.py"
            },
            "ContextCoder": {
                "base_classes": ["Coder"],
                "file_path": "aider/coders/context_coder.py"
            },
            "ArchitectCoder": {
                "base_classes": ["Coder"],
                "file_path": "aider/coders/architect_coder.py"
            },
            "RepoMap": {
                "base_classes": [],
                "file_path": "aider/repomap.py"
            }
        }

        if class_name not in class_inheritance:
            return []

        base_classes = []
        for base_class_name in class_inheritance[class_name]["base_classes"]:
            if base_class_name in class_inheritance:
                base_classes.append({
                    "class_name": base_class_name,
                    "file_path": os.path.join(project_path, class_inheritance[base_class_name]["file_path"]),
                    "module_path": self._get_module_path(class_inheritance[base_class_name]["file_path"])
                })

        # Update the cache
        self.class_inheritance_cache[cache_key] = base_classes
        self._update_cache_timestamp(cache_key)

        return base_classes

    def get_derived_classes_of(self, project_path: str, model_name: str, class_name: str, file_path: str = None) -> List[Dict]:
        """
        Get the derived classes of the specified class.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use
            class_name: Name of the class to analyze
            file_path: Path to the file containing the class (optional)

        Returns:
            A list of dictionaries with derived class information
        """
        # Check if we have this information in the cache
        cache_key = (project_path, class_name, 'derived_classes')
        if cache_key in self.class_inheritance_cache and self._is_cache_valid(cache_key):
            return self.class_inheritance_cache[cache_key]

        # Try to use dynamic analysis with RepoMap
        if REPOMAP_AVAILABLE:
            try:
                # Get class inheritance information from RepoMap
                class_info = self._extract_class_info_from_repomap(project_path, model_name)

                if class_name in class_info:
                    derived_classes = []
                    for derived_class_name in class_info[class_name]["derived_classes"]:
                        if derived_class_name in class_info:
                            derived_classes.append({
                                "class_name": derived_class_name,
                                "file_path": os.path.join(project_path, class_info[derived_class_name]["file_path"]),
                                "module_path": self._get_module_path(class_info[derived_class_name]["file_path"])
                            })

                    # Update the cache
                    self.class_inheritance_cache[cache_key] = derived_classes
                    self._update_cache_timestamp(cache_key)

                    return derived_classes
            except Exception as e:
                print(f"Error using RepoMap for class inheritance analysis: {e}")
                # Fall back to static mapping

        # Fall back to static mapping if RepoMap is not available or fails
        # Example class inheritance for Aider's codebase
        class_inheritance = {
            "Coder": {
                "derived_classes": ["EditBlockCoder", "WholeFileCoder", "UDiffCoder", "PatchCoder", "ContextCoder", "ArchitectCoder"],
                "file_path": "aider/coders/base_coder.py"
            },
            "EditBlockCoder": {
                "derived_classes": [],
                "file_path": "aider/coders/editblock_coder.py"
            },
            "WholeFileCoder": {
                "derived_classes": [],
                "file_path": "aider/coders/wholefile_coder.py"
            },
            "UDiffCoder": {
                "derived_classes": [],
                "file_path": "aider/coders/udiff_coder.py"
            },
            "PatchCoder": {
                "derived_classes": [],
                "file_path": "aider/coders/patch_coder.py"
            },
            "ContextCoder": {
                "derived_classes": [],
                "file_path": "aider/coders/context_coder.py"
            },
            "ArchitectCoder": {
                "derived_classes": [],
                "file_path": "aider/coders/architect_coder.py"
            },
            "RepoMap": {
                "derived_classes": [],
                "file_path": "aider/repomap.py"
            }
        }

        if class_name not in class_inheritance:
            return []

        derived_classes = []
        for derived_class_name in class_inheritance[class_name]["derived_classes"]:
            if derived_class_name in class_inheritance:
                derived_classes.append({
                    "class_name": derived_class_name,
                    "file_path": os.path.join(project_path, class_inheritance[derived_class_name]["file_path"]),
                    "module_path": self._get_module_path(class_inheritance[derived_class_name]["file_path"])
                })

        # Update the cache
        self.class_inheritance_cache[cache_key] = derived_classes
        self._update_cache_timestamp(cache_key)

        return derived_classes

    def _get_module_path(self, file_path: str) -> str:
        """
        Convert a file path to a module path.

        Args:
            file_path: Relative path to a Python file

        Returns:
            Module path (e.g., 'aider.coders.base_coder')
        """
        if not file_path.endswith('.py'):
            return file_path

        # Remove .py extension
        module_path = file_path[:-3]

        # Replace directory separators with dots
        module_path = module_path.replace('/', '.').replace('\\', '.')

        # Remove __init__ from the end
        if module_path.endswith('.__init__'):
            module_path = module_path[:-9]

        return module_path


class MidLevelIRGenerator:
    """
    Generates Mid-Level Intermediate Representation (IR) for codebases.

    This class analyzes a codebase and generates a structured JSON representation
    that includes modules, dependencies, entities, and detailed metadata about
    functions, classes, and their relationships.
    """

    def __init__(self, project_manager: AiderProjectManager):
        """
        Initialize the Mid-Level IR Generator.

        Args:
            project_manager: The AiderProjectManager instance to use for analysis
        """
        self.project_manager = project_manager

    def generate_mid_level_ir(self, project_path: str, model_name: str = "gpt-3.5-turbo") -> Dict:
        """
        Generate the complete Mid-Level IR for a project.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use for analysis

        Returns:
            Dictionary containing the Mid-Level IR structure
        """
        print("🔍 Generating Mid-Level IR...")

        # Get all Python files in the project
        python_files = self._get_python_files(project_path)
        print(f"   Found {len(python_files)} Python files")

        # Analyze each file as a module
        modules = []
        for file_path in python_files:
            try:
                module_info = self._analyze_module(project_path, file_path, model_name)
                if module_info:
                    modules.append(module_info)
            except Exception as e:
                print(f"   Error analyzing {file_path}: {e}")
                continue

        print(f"   Generated IR for {len(modules)} modules")

        # Calculate inter-module dependencies
        self._calculate_module_dependencies(modules, project_path, model_name)

        return {
            "modules": modules,
            "metadata": {
                "generated_at": time.time(),
                "project_path": project_path,
                "total_modules": len(modules),
                "generator_version": "1.0.0"
            }
        }

    def _get_python_files(self, project_path: str) -> List[str]:
        """Get all Python files in the project."""
        python_files = []
        for root, _, files in os.walk(project_path):
            # Skip common non-source directories
            if any(skip in root for skip in ["__pycache__", ".git", ".pytest_cache", "node_modules"]):
                continue

            for file in files:
                if file.endswith('.py') and not file.startswith('.'):
                    python_files.append(os.path.join(root, file))

        return python_files

    def _analyze_module(self, project_path: str, file_path: str, model_name: str) -> Optional[Dict]:
        """
        Analyze a single file as a module.

        Args:
            project_path: Path to the project root
            file_path: Path to the file to analyze
            model_name: Name of the model to use

        Returns:
            Dictionary containing module information or None if analysis fails
        """
        try:
            rel_path = os.path.relpath(file_path, project_path)
            module_name = self._get_module_name(rel_path)

            # Get basic file info
            loc = self._count_lines_of_code(file_path)

            # Get symbols defined in the file
            symbols = self.project_manager.get_symbols_defined_in_file(project_path, model_name, file_path)

            # Analyze entities (functions, classes)
            entities = self._analyze_entities(project_path, file_path, symbols, model_name)

            module_info = {
                "name": module_name,
                "file": rel_path,
                "loc": loc,
                "dependencies": [],  # Will be filled later
                "entities": entities
            }

            return module_info

        except Exception as e:
            print(f"Error analyzing module {file_path}: {e}")
            return None

    def _get_module_name(self, rel_path: str) -> str:
        """Convert a file path to a module name."""
        # Remove .py extension and convert path separators to dots
        module_name = rel_path.replace('.py', '').replace('/', '.').replace('\\', '.')

        # Remove __init__ from the end
        if module_name.endswith('.__init__'):
            module_name = module_name[:-9]

        # Use the last component as the module name for simplicity
        return module_name.split('.')[-1] if '.' in module_name else module_name

    def _count_lines_of_code(self, file_path: str) -> int:
        """Count lines of code in a file (excluding comments and blank lines)."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            loc = 0
            for line in lines:
                stripped = line.strip()
                # Skip empty lines and comments
                if stripped and not stripped.startswith('#'):
                    loc += 1

            return loc
        except Exception:
            return 0

    def _analyze_entities(self, project_path: str, file_path: str, symbols: Dict[str, List[str]], model_name: str) -> List[Dict]:
        """
        Analyze entities (functions, classes) in a file.

        Args:
            project_path: Path to the project root
            file_path: Path to the file to analyze
            symbols: Dictionary of symbols defined in the file
            model_name: Name of the model to use

        Returns:
            List of entity dictionaries
        """
        entities = []

        try:
            # Read the file content for detailed analysis
            with open(file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()

            # Analyze functions
            for func_name in symbols.get('functions', []):
                entity = self._analyze_function(file_content, func_name, project_path, file_path, model_name)
                if entity:
                    entities.append(entity)

            # Analyze classes
            for class_name in symbols.get('classes', []):
                entity = self._analyze_class(file_content, class_name, project_path, file_path, model_name)
                if entity:
                    entities.append(entity)

        except Exception as e:
            print(f"Error analyzing entities in {file_path}: {e}")

        return entities

    def _analyze_function(self, file_content: str, func_name: str, project_path: str, file_path: str, model_name: str) -> Optional[Dict]:
        """
        Analyze a function and extract detailed metadata.

        Args:
            file_content: Content of the file
            func_name: Name of the function to analyze
            project_path: Path to the project root
            file_path: Path to the file
            model_name: Name of the model to use

        Returns:
            Dictionary containing function metadata or None if analysis fails
        """
        try:
            # Find function definition using regex
            func_pattern = rf'def\s+{re.escape(func_name)}\s*\((.*?)\)(?:\s*->\s*([^:]+))?:'
            match = re.search(func_pattern, file_content, re.MULTILINE | re.DOTALL)

            if not match:
                return None

            params_str = match.group(1) if match.group(1) else ""
            return_type = match.group(2).strip() if match.group(2) else "None"

            # Parse parameters
            params = self._parse_function_parameters(params_str)

            # Extract docstring
            docstring = self._extract_docstring(file_content, func_name)

            # Analyze function calls within this function
            calls = self._analyze_function_calls(file_content, func_name)

            # Determine side effects and errors
            side_effects = self._analyze_side_effects(file_content, func_name)
            errors = self._analyze_potential_errors(file_content, func_name)

            # Calculate criticality and change risk
            criticality = self._calculate_criticality(func_name, project_path, model_name)
            change_risk = self._calculate_change_risk(func_name, calls, project_path, model_name)

            return {
                "type": "function",
                "name": func_name,
                "doc": docstring or f"Function {func_name}",
                "params": params,
                "returns": return_type,
                "calls": calls,
                "used_by": [],  # Will be filled during dependency analysis
                "side_effects": side_effects,
                "errors": errors,
                "criticality": criticality,
                "change_risk": change_risk
            }

        except Exception as e:
            print(f"Error analyzing function {func_name}: {e}")
            return None

    def _analyze_class(self, file_content: str, class_name: str, project_path: str, file_path: str, model_name: str) -> Optional[Dict]:
        """
        Analyze a class and extract detailed metadata.

        Args:
            file_content: Content of the file
            class_name: Name of the class to analyze
            project_path: Path to the project root
            file_path: Path to the file
            model_name: Name of the model to use

        Returns:
            Dictionary containing class metadata or None if analysis fails
        """
        try:
            # Find class definition
            class_pattern = rf'class\s+{re.escape(class_name)}\s*(?:\((.*?)\))?:'
            match = re.search(class_pattern, file_content, re.MULTILINE)

            if not match:
                return None

            base_classes_str = match.group(1) if match.group(1) else ""
            base_classes = [bc.strip() for bc in base_classes_str.split(',') if bc.strip()] if base_classes_str else []

            # Extract docstring
            docstring = self._extract_class_docstring(file_content, class_name)

            # Find methods in the class
            methods = self._extract_class_methods(file_content, class_name)

            # Calculate criticality and change risk
            criticality = self._calculate_criticality(class_name, project_path, model_name)
            change_risk = self._calculate_change_risk(class_name, methods, project_path, model_name)

            return {
                "type": "class",
                "name": class_name,
                "doc": docstring or f"Class {class_name}",
                "params": base_classes,  # Base classes as "parameters"
                "returns": "class_instance",
                "calls": methods,  # Methods as "calls"
                "used_by": [],  # Will be filled during dependency analysis
                "side_effects": ["creates_instance"],
                "errors": ["AttributeError", "TypeError"],
                "criticality": criticality,
                "change_risk": change_risk
            }

        except Exception as e:
            print(f"Error analyzing class {class_name}: {e}")
            return None

    def _parse_function_parameters(self, params_str: str) -> List[str]:
        """Parse function parameters from parameter string."""
        if not params_str.strip():
            return []

        params = []
        # Simple parameter parsing - split by comma and clean up
        for param in params_str.split(','):
            param = param.strip()
            if param:
                # Extract just the parameter name (before : or =)
                param_name = param.split(':')[0].split('=')[0].strip()
                if param_name and param_name != 'self':
                    params.append(param_name)

        return params

    def _extract_docstring(self, file_content: str, func_name: str) -> Optional[str]:
        """Extract docstring for a function."""
        try:
            # Find function definition and look for docstring
            func_pattern = rf'def\s+{re.escape(func_name)}\s*\([^)]*\).*?:\s*(?:\n\s*"""(.*?)"""|\'\'\'(.*?)\'\'\')'
            match = re.search(func_pattern, file_content, re.MULTILINE | re.DOTALL)

            if match:
                docstring = match.group(1) or match.group(2)
                if docstring:
                    # Clean up the docstring
                    lines = docstring.strip().split('\n')
                    if lines:
                        return lines[0].strip()  # Return first line

            return None
        except Exception:
            return None

    def _extract_class_docstring(self, file_content: str, class_name: str) -> Optional[str]:
        """Extract docstring for a class."""
        try:
            # Find class definition and look for docstring
            class_pattern = rf'class\s+{re.escape(class_name)}.*?:\s*(?:\n\s*"""(.*?)"""|\'\'\'(.*?)\'\'\')'
            match = re.search(class_pattern, file_content, re.MULTILINE | re.DOTALL)

            if match:
                docstring = match.group(1) or match.group(2)
                if docstring:
                    # Clean up the docstring
                    lines = docstring.strip().split('\n')
                    if lines:
                        return lines[0].strip()  # Return first line

            return None
        except Exception:
            return None

    def _analyze_function_calls(self, file_content: str, func_name: str) -> List[str]:
        """Analyze function calls within a function."""
        try:
            # Find the function body
            func_pattern = rf'def\s+{re.escape(func_name)}\s*\([^)]*\).*?:(.*?)(?=\ndef|\nclass|\Z)'
            match = re.search(func_pattern, file_content, re.MULTILINE | re.DOTALL)

            if not match:
                return []

            func_body = match.group(1)

            # Find function calls in the body
            call_pattern = r'(\w+)\s*\('
            calls = []

            for match in re.finditer(call_pattern, func_body):
                call_name = match.group(1)
                # Filter out common keywords and built-ins
                if call_name not in ['if', 'for', 'while', 'try', 'with', 'print', 'len', 'str', 'int', 'float', 'list', 'dict', 'set']:
                    if call_name not in calls:
                        calls.append(call_name)

            return calls[:10]  # Limit to first 10 calls
        except Exception:
            return []

    def _extract_class_methods(self, file_content: str, class_name: str) -> List[str]:
        """Extract methods from a class."""
        try:
            # Find the class body
            class_pattern = rf'class\s+{re.escape(class_name)}.*?:(.*?)(?=\nclass|\ndef(?!\s+\w+\s*\(self)|\Z)'
            match = re.search(class_pattern, file_content, re.MULTILINE | re.DOTALL)

            if not match:
                return []

            class_body = match.group(1)

            # Find method definitions
            method_pattern = r'def\s+(\w+)\s*\('
            methods = []

            for match in re.finditer(method_pattern, class_body):
                method_name = match.group(1)
                if method_name not in ['__init__', '__str__', '__repr__']:  # Skip common magic methods
                    if method_name not in methods:
                        methods.append(method_name)

            return methods
        except Exception:
            return []

    def _analyze_side_effects(self, file_content: str, func_name: str) -> List[str]:
        """Analyze potential side effects of a function."""
        side_effects = []

        try:
            # Find the function body
            func_pattern = rf'def\s+{re.escape(func_name)}\s*\([^)]*\).*?:(.*?)(?=\ndef|\nclass|\Z)'
            match = re.search(func_pattern, file_content, re.MULTILINE | re.DOTALL)

            if not match:
                return side_effects

            func_body = match.group(1)

            # Check for common side effects
            if 'print(' in func_body or 'logging.' in func_body:
                side_effects.append('writes_log')
            if 'open(' in func_body or 'write(' in func_body:
                side_effects.append('modifies_file')
            if 'self.' in func_body and '=' in func_body:
                side_effects.append('modifies_state')
            if 'global ' in func_body:
                side_effects.append('modifies_global')
            if 'import ' in func_body:
                side_effects.append('dynamic_import')

        except Exception:
            pass

        return side_effects if side_effects else ['none']

    def _analyze_potential_errors(self, file_content: str, func_name: str) -> List[str]:
        """Analyze potential errors a function might raise."""
        errors = []

        try:
            # Find the function body
            func_pattern = rf'def\s+{re.escape(func_name)}\s*\([^)]*\).*?:(.*?)(?=\ndef|\nclass|\Z)'
            match = re.search(func_pattern, file_content, re.MULTILINE | re.DOTALL)

            if not match:
                return ['RuntimeError']

            func_body = match.group(1)

            # Check for explicit raises
            if 'raise ' in func_body:
                raise_pattern = r'raise\s+(\w+)'
                for match in re.finditer(raise_pattern, func_body):
                    error_type = match.group(1)
                    if error_type not in errors:
                        errors.append(error_type)

            # Check for common error-prone operations
            if '[' in func_body and ']' in func_body:
                errors.append('IndexError')
            if 'open(' in func_body:
                errors.append('FileNotFoundError')
            if 'int(' in func_body or 'float(' in func_body:
                errors.append('ValueError')
            if '/' in func_body:
                errors.append('ZeroDivisionError')

        except Exception:
            pass

        return errors if errors else ['RuntimeError']

    def _calculate_criticality(self, symbol_name: str, project_path: str, model_name: str) -> str:
        """Calculate the criticality level of a symbol."""
        try:
            # Get files that use this symbol
            used_by_count = 0

            # Check if this symbol is used across multiple files
            if self.project_manager.dependency_data:
                central_files = self.project_manager.dependency_data.get("central_files", [])
                for file_info in central_files:
                    if symbol_name.lower() in file_info.get("file", "").lower():
                        used_by_count = file_info.get("references", 0)
                        break

            # Determine criticality based on usage
            if used_by_count > 50:
                return "high"
            elif used_by_count > 20:
                return "medium"
            else:
                return "low"

        except Exception:
            return "medium"  # Default to medium if analysis fails

    def _calculate_change_risk(self, symbol_name: str, dependencies: List[str], project_path: str, model_name: str) -> str:
        """Calculate the change risk level of a symbol."""
        try:
            # Risk factors:
            # 1. Number of dependencies
            # 2. Complexity of dependencies
            # 3. Usage across the codebase

            risk_score = 0

            # Factor 1: Number of dependencies
            dep_count = len(dependencies)
            if dep_count > 10:
                risk_score += 3
            elif dep_count > 5:
                risk_score += 2
            elif dep_count > 2:
                risk_score += 1

            # Factor 2: Check if symbol is in a central file
            if self.project_manager.dependency_data:
                central_files = self.project_manager.dependency_data.get("central_files", [])
                for file_info in central_files[:10]:  # Top 10 central files
                    if symbol_name.lower() in file_info.get("file", "").lower():
                        risk_score += 2
                        break

            # Factor 3: Check for complex dependencies
            complex_deps = ['database', 'network', 'file', 'thread', 'async']
            for dep in dependencies:
                if any(complex_word in dep.lower() for complex_word in complex_deps):
                    risk_score += 1
                    break

            # Determine risk level
            if risk_score >= 5:
                return "high"
            elif risk_score >= 3:
                return "medium"
            else:
                return "low"

        except Exception:
            return "medium"  # Default to medium if analysis fails

    def _calculate_module_dependencies(self, modules: List[Dict], project_path: str, model_name: str) -> None:
        """Calculate dependencies between modules."""
        print("   Calculating module dependencies...")

        # Create a mapping of module names to their files
        module_to_file = {module["name"]: module["file"] for module in modules}
        file_to_module = {module["file"]: module for module in modules}

        for module in modules:
            try:
                file_path = os.path.join(project_path, module["file"])

                # Skip dependency calculation for now to avoid file path issues
                # We'll implement a simpler approach
                dependencies = self._calculate_simple_dependencies(module, modules, project_path)

                module["dependencies"] = dependencies

                # Also update "used_by" for entities with a simpler approach
                self._update_entity_usage_simple(module, modules)

            except Exception as e:
                print(f"   Error calculating dependencies for {module['name']}: {e}")
                module["dependencies"] = []

    def _calculate_dependency_strength(self, source_file: str, target_file: str, project_path: str, model_name: str) -> str:
        """Calculate the strength of dependency between two files."""
        try:
            # Get symbol references between files
            source_path = os.path.join(project_path, source_file)
            target_path = os.path.join(project_path, target_file)

            references = self.project_manager.get_symbol_references_between_files(
                project_path, source_path, target_path, model_name
            )

            # Count total references
            total_refs = sum(len(refs) for refs in references.values())

            # Determine strength based on reference count
            if total_refs > 10:
                return "strong"
            elif total_refs > 3:
                return "medium"
            else:
                return "weak"

        except Exception:
            return "weak"  # Default to weak if analysis fails

    def _update_entity_usage(self, module: Dict, all_modules: List[Dict], project_path: str, model_name: str) -> None:
        """Update the 'used_by' field for entities in a module."""
        try:
            file_path = os.path.join(project_path, module["file"])

            # Get files that import this module
            importing_files = self.project_manager.get_files_that_import(project_path, file_path, model_name)

            # Convert file paths to module names
            used_by_modules = []
            for importing_file in importing_files:
                importing_rel_path = os.path.relpath(importing_file, project_path) if os.path.isabs(importing_file) else importing_file

                # Find the corresponding module
                for other_module in all_modules:
                    if other_module["file"] == importing_rel_path:
                        used_by_modules.append(other_module["name"])
                        break

            # Update entities
            for entity in module.get("entities", []):
                entity["used_by"] = used_by_modules[:5]  # Limit to first 5 for brevity

        except Exception as e:
            print(f"   Error updating entity usage for {module['name']}: {e}")

    def _calculate_simple_dependencies(self, module: Dict, all_modules: List[Dict], project_path: str) -> List[Dict]:
        """Calculate dependencies using a simpler approach based on imports in the file."""
        dependencies = []

        try:
            file_path = os.path.join(project_path, module["file"])

            # Read the file and look for import statements
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Find import statements
            import_lines = []
            for line in content.split('\n'):
                stripped = line.strip()
                if stripped.startswith('import ') or stripped.startswith('from '):
                    import_lines.append(stripped)

            # Check which other modules might be imported
            for other_module in all_modules:
                if other_module["name"] == module["name"]:
                    continue

                # Check if this module is referenced in imports
                module_referenced = False
                for import_line in import_lines:
                    # Simple heuristic: check if module name appears in import
                    if other_module["name"].lower() in import_line.lower():
                        module_referenced = True
                        break

                    # Check if file name (without extension) appears
                    file_base = other_module["file"].replace('.py', '').replace('/', '.').replace('\\', '.')
                    if file_base.lower() in import_line.lower():
                        module_referenced = True
                        break

                if module_referenced:
                    # Simple strength calculation based on number of import lines
                    strength = "weak"
                    if len(import_lines) > 10:
                        strength = "strong"
                    elif len(import_lines) > 5:
                        strength = "medium"

                    dependencies.append({
                        "module": other_module["name"],
                        "strength": strength
                    })

            return dependencies[:10]  # Limit to first 10 dependencies

        except Exception as e:
            print(f"   Error calculating simple dependencies for {module['name']}: {e}")
            return []

    def _update_entity_usage_simple(self, module: Dict, all_modules: List[Dict]) -> None:
        """Update entity usage with a simpler approach."""
        try:
            # For each entity in this module, find which other modules might use it
            for entity in module.get("entities", []):
                used_by = []

                # Check other modules for potential usage
                for other_module in all_modules:
                    if other_module["name"] == module["name"]:
                        continue

                    # Simple heuristic: if entity name appears in other module's entities' calls
                    for other_entity in other_module.get("entities", []):
                        if entity["name"] in other_entity.get("calls", []):
                            if other_module["name"] not in used_by:
                                used_by.append(other_module["name"])

                entity["used_by"] = used_by[:5]  # Limit to first 5

        except Exception as e:
            print(f"   Error updating simple entity usage for {module['name']}: {e}")


class AiderIntegrationService:
    """
    Service for integrating with Aider and exposing its functionality.

    This service provides methods to analyze and understand the dependencies
    between files and classes in a codebase. It uses Aider's RepoMap for
    dynamic analysis when available, and falls back to static analysis
    when necessary.

    It also provides surgical context extraction capabilities to extract
    focused code snippets around dependency interaction points.

    Enhanced with Multi-Turn Reasoning Loop (IAA Protocol) for complex
    iterative analysis workflows.
    """

    def __init__(self, cache_ttl: int = 3600):
        """
        Initialize the integration service.

        Args:
            cache_ttl: Time-to-live for cache entries in seconds (default: 1 hour)
        """
        self.project_manager = AiderProjectManager(cache_ttl=cache_ttl)
        self.context_extractor = None  # Lazy initialization
        self.ir_generator = None  # Lazy initialization
        self.context_selector = None  # Lazy initialization
        self.iterative_engine = None  # Lazy initialization for IAA Protocol

    def _get_ir_generator(self):
        """Get the Mid-Level IR generator, initializing it if necessary."""
        if self.ir_generator is None:
            # Try to use the new modular pipeline first
            try:
                from mid_level_ir import MidLevelIRPipeline

                # Configure the modular pipeline
                config = {
                    'verbose': False,  # Keep quiet for integration
                    'file_scanner': {
                        'exclude_dirs': ['__pycache__', '.git', '.pytest_cache', 'node_modules'],
                        'max_file_size_mb': 10
                    },
                    'entity_extractor': {
                        'extract_variables': True,
                        'extract_constants': True,
                        'min_function_lines': 1
                    },
                    'call_graph_builder': {
                        'max_calls_per_entity': 15,
                        'include_builtin_calls': False
                    },
                    'dependency_analyzer': {
                        'include_stdlib': False,
                        'include_external': True
                    },
                    'ir_builder': {
                        'include_metadata': True,
                        'include_ast_info': False,  # Keep output size manageable
                        'pretty_print': True
                    }
                }

                self.ir_generator = MidLevelIRPipeline(config)
                print("✅ Using enhanced modular Mid-Level IR pipeline")

            except ImportError:
                # Fall back to the original implementation
                self.ir_generator = MidLevelIRGenerator(self.project_manager)
                print("⚠️  Using legacy Mid-Level IR generator (modular pipeline not available)")

        return self.ir_generator

    def generate_mid_level_ir(self, project_path: str, model_name: str = "gpt-3.5-turbo") -> Dict:
        """
        Generate Mid-Level Intermediate Representation for the project.

        Args:
            project_path: Path to the project root
            model_name: Name of the model to use for analysis

        Returns:
            Dictionary containing the Mid-Level IR structure
        """
        generator = self._get_ir_generator()

        # Check if we're using the new modular pipeline
        if hasattr(generator, 'generate_ir'):
            # New modular pipeline
            return generator.generate_ir(project_path)
        else:
            # Legacy generator
            return generator.generate_mid_level_ir(project_path, model_name)

    def _get_context_selector(self, project_path: str, max_tokens: int = 8000):
        """Get the Intelligent Context Selector, initializing it if necessary."""
        if self.context_selector is None:
            try:
                from intelligent_context_selector import IntelligentContextSelector

                # Generate IR data if not available
                ir_data = self.generate_mid_level_ir(project_path)

                # Create the context selector
                self.context_selector = IntelligentContextSelector(ir_data, max_tokens)
                print("✅ Intelligent Context Selector initialized")

            except ImportError as e:
                print(f"⚠️ Could not import IntelligentContextSelector: {e}")
                self.context_selector = None
            except Exception as e:
                print(f"⚠️ Error initializing context selector: {e}")
                self.context_selector = None

        return self.context_selector

    def select_intelligent_context(self, project_path: str, task_description: str,
                                 task_type: str = "general_analysis",
                                 focus_entities: list = None, max_tokens: int = 8000):
        """
        Select the most relevant code context for a given task using AI-powered analysis.

        Args:
            project_path: Path to the project root
            task_description: Natural language description of the task
            task_type: Type of task (debugging, feature_development, refactoring, etc.)
            focus_entities: Optional list of specific entities to focus on
            max_tokens: Maximum token budget for context selection

        Returns:
            Dictionary containing the selected context bundle with entities and metadata
        """
        try:
            # Get the context selector
            selector = self._get_context_selector(project_path, max_tokens)

            if selector is None:
                return {
                    'error': 'Context selector not available',
                    'fallback': 'Use traditional context extraction methods'
                }

            # Map string task type to enum
            from intelligent_context_selector import TaskType
            task_type_map = {
                'debugging': TaskType.DEBUGGING,
                'feature_development': TaskType.FEATURE_DEVELOPMENT,
                'code_review': TaskType.CODE_REVIEW,
                'refactoring': TaskType.REFACTORING,
                'documentation': TaskType.DOCUMENTATION,
                'testing': TaskType.TESTING,
                'general_analysis': TaskType.GENERAL_ANALYSIS
            }

            task_enum = task_type_map.get(task_type.lower(), TaskType.GENERAL_ANALYSIS)

            # Select optimal context
            context_bundle = selector.select_optimal_context(
                task_description=task_description,
                task_type=task_enum,
                focus_entities=focus_entities
            )

            # Analyze context quality
            quality_analysis = selector.analyze_context_quality(context_bundle)

            # Convert to dictionary format for easy consumption
            result = {
                'task_description': context_bundle.task_description,
                'task_type': context_bundle.task_type.value,
                'total_entities': len(context_bundle.entities),
                'total_tokens': context_bundle.total_tokens,
                'selection_rationale': context_bundle.selection_rationale,
                'quality_metrics': quality_analysis,
                'entities': []
            }

            # Add entity details
            for entity in context_bundle.entities:
                entity_info = {
                    'module_name': entity.module_name,
                    'entity_name': entity.entity_name,
                    'entity_type': entity.entity_type,
                    'file_path': entity.file_path,
                    'criticality': entity.criticality,
                    'change_risk': entity.change_risk,
                    'relevance_score': entity.relevance_score,
                    'priority': entity.priority.value,
                    'token_estimate': entity.token_estimate,
                    'dependency_depth': entity.dependency_depth,
                    'used_by': entity.used_by,
                    'calls': entity.calls,
                    'side_effects': entity.side_effects,
                    'errors': entity.errors
                }
                result['entities'].append(entity_info)

            # Sort entities by relevance score
            result['entities'].sort(key=lambda e: e['relevance_score'], reverse=True)

            return result

        except Exception as e:
            print(f"Error in intelligent context selection: {e}")
            return {
                'error': str(e),
                'fallback': 'Use traditional context extraction methods'
            }

    def _get_iterative_engine(self, project_path: str, max_tokens: int = 8000):
        """Get the Iterative Analysis Engine, initializing it if necessary."""
        if self.iterative_engine is None:
            try:
                from iterative_analysis_engine import IterativeAnalysisEngine

                # Get the context selector first
                context_selector = self._get_context_selector(project_path, max_tokens)

                if context_selector is None:
                    print("⚠️ Cannot initialize Iterative Analysis Engine without context selector")
                    return None

                # Create the iterative engine
                self.iterative_engine = IterativeAnalysisEngine(
                    context_selector=context_selector,
                    max_iterations=5
                )
                print("✅ Iterative Analysis Engine initialized")

            except ImportError as e:
                print(f"⚠️ Could not import IterativeAnalysisEngine: {e}")
                self.iterative_engine = None
            except Exception as e:
                print(f"⚠️ Error initializing iterative engine: {e}")
                self.iterative_engine = None

        return self.iterative_engine

    def analyze_with_multi_turn_reasoning(self, project_path: str, task_description: str,
                                        task_type: str = "general_analysis",
                                        focus_entities: list = None, max_iterations: int = 5,
                                        max_tokens: int = 8000):
        """
        Perform multi-turn reasoning analysis using the IAA Protocol.

        This method implements the Iterative Analysis Accumulation protocol that builds
        understanding across multiple iterations, using intelligent context selection
        and analysis memory to progressively improve understanding.

        Args:
            project_path: Path to the project root
            task_description: Natural language description of the analysis task
            task_type: Type of task (debugging, feature_development, refactoring, etc.)
            focus_entities: Optional list of specific entities to focus on
            max_iterations: Maximum number of analysis iterations
            max_tokens: Maximum token budget for context selection

        Returns:
            Dictionary containing comprehensive multi-turn analysis results
        """
        try:
            print(f"🧠 Starting Multi-Turn Reasoning Analysis")
            print(f"   Task: {task_description}")
            print(f"   Type: {task_type}")
            print(f"   Max iterations: {max_iterations}")

            # Get the iterative analysis engine
            engine = self._get_iterative_engine(project_path, max_tokens)

            if engine is None:
                return {
                    'error': 'Iterative Analysis Engine not available',
                    'fallback': 'Use single-turn intelligent context selection'
                }

            # Update max iterations if specified
            if max_iterations != engine.max_iterations:
                engine.max_iterations = max_iterations

            # Perform iterative analysis
            analysis_results = engine.analyze_incrementally(
                task=task_description,
                task_type=task_type,
                focus_entities=focus_entities
            )

            # Add metadata about the analysis
            analysis_results['analysis_metadata'] = {
                'project_path': project_path,
                'max_tokens': max_tokens,
                'engine_version': 'IAA Protocol v1.0',
                'analysis_type': 'multi_turn_reasoning'
            }

            print(f"✅ Multi-Turn Analysis Complete")
            print(f"   Iterations: {analysis_results['total_iterations']}")
            print(f"   Confidence: {analysis_results['overall_confidence']:.2f}")
            print(f"   Entities: {len(analysis_results['entity_summaries'])}")

            return analysis_results

        except Exception as e:
            print(f"⚠️ Error in multi-turn reasoning analysis: {e}")
            return {
                'error': str(e),
                'fallback': 'Use single-turn intelligent context selection'
            }

    def get_intelligent_context(self, project_path: str, task_description: str,
                              task_type: str = "feature_development",
                              focus_entities: list = None, max_tokens: int = 8000):
        """
        Alias for select_intelligent_context for consistency with documentation.

        This method provides the same functionality as select_intelligent_context
        but with a name that matches the documentation examples.
        """
        return self.select_intelligent_context(
            project_path=project_path,
            task_description=task_description,
            task_type=task_type,
            focus_entities=focus_entities,
            max_tokens=max_tokens
        )

    def find_relevant_code_for_feature(self, project_path: str,
                                     feature_description: str,
                                     focus_areas: list = None) -> dict:
        """
        Find relevant code for a new feature with user-friendly output.

        This is the main entry point for the Intelligent Code Discovery feature.
        It combines intelligent context selection and multi-turn reasoning to provide
        organized, actionable results for feature development.

        Args:
            project_path: Path to the project
            feature_description: Natural language description of the feature
            focus_areas: Key areas/keywords to focus on (optional)

        Returns:
            Dictionary with organized, actionable results including:
            - critical_entities: High-risk entities that need careful handling
            - related_entities: Relevant entities that may need modification
            - safe_entities: Safe integration points to start with
            - implementation_guidance: Step-by-step guidance
            - dependency_map: Relationship mapping
            - recommendations: Actionable recommendations
        """
        try:
            print(f"🔍 Starting Intelligent Code Discovery")
            print(f"   Feature: {feature_description}")
            print(f"   Focus areas: {focus_areas}")

            # Use existing intelligent context selection
            context_result = self.get_intelligent_context(
                project_path=project_path,
                task_description=feature_description,
                task_type="feature_development",
                focus_entities=focus_areas
            )

            # Check if context selection was successful
            if 'error' in context_result:
                return {
                    'error': f"Context selection failed: {context_result['error']}",
                    'fallback': 'Use traditional code exploration methods'
                }

            # Use existing multi-turn reasoning for deeper analysis
            analysis_results = self.analyze_with_multi_turn_reasoning(
                project_path=project_path,
                task_description=feature_description,
                task_type="feature_development",
                focus_entities=focus_areas,
                max_iterations=3
            )

            # Check if analysis was successful
            if 'error' in analysis_results:
                print(f"⚠️ Multi-turn analysis failed, using single-turn results")
                analysis_results = {'global_insights': [], 'overall_confidence': 0.5}

            # Format into user-friendly structure
            result = {
                "analysis_summary": {
                    "total_entities_analyzed": len(context_result.get('entities', [])),
                    "entities_selected": context_result.get('total_entities', 0),
                    "selection_confidence": analysis_results.get('overall_confidence', 0.0),
                    "processing_time": "N/A",  # Could be calculated if needed
                    "task_description": feature_description,
                    "focus_areas": focus_areas or []
                },
                "critical_entities": self._extract_critical_entities(context_result),
                "related_entities": self._extract_related_entities(context_result),
                "safe_entities": self._extract_safe_entities(context_result),
                "implementation_guidance": self._generate_guidance(analysis_results, context_result),
                "dependency_map": self._extract_dependency_map(context_result),
                "recommendations": self._generate_recommendations(analysis_results, context_result)
            }

            print(f"✅ Code Discovery Complete")
            print(f"   Critical entities: {len(result['critical_entities'])}")
            print(f"   Safe entities: {len(result['safe_entities'])}")
            print(f"   Related entities: {len(result['related_entities'])}")

            return result

        except Exception as e:
            print(f"⚠️ Error in intelligent code discovery: {e}")
            return {
                'error': str(e),
                'fallback': 'Use traditional code exploration methods'
            }

    def _extract_critical_entities(self, context_result: dict) -> list:
        """Extract and format critical entities for user display."""
        critical_entities = []

        entities = context_result.get('entities', [])
        for entity in entities:
            # Consider entities critical if they have high criticality or high change risk
            if (entity.get('criticality') == 'high' or
                entity.get('change_risk') == 'high' or
                len(entity.get('used_by', [])) > 10):

                critical_entities.append({
                    "entity_name": entity.get('entity_name', 'Unknown'),
                    "entity_type": entity.get('entity_type', 'Unknown'),
                    "file_path": entity.get('file_path', 'Unknown'),
                    "module_name": entity.get('module_name', 'Unknown'),
                    "criticality": entity.get('criticality', 'unknown'),
                    "change_risk": entity.get('change_risk', 'unknown'),
                    "used_by_count": len(entity.get('used_by', [])),
                    "dependencies_count": len(entity.get('calls', [])),
                    "relevance_score": entity.get('relevance_score', 0.0),
                    "risk_explanation": self._explain_risk(entity)
                })

        # Sort by usage count (most used first) then by relevance score
        return sorted(critical_entities,
                     key=lambda x: (x["used_by_count"], x["relevance_score"]),
                     reverse=True)

    def _extract_safe_entities(self, context_result: dict) -> list:
        """Extract and format safe integration points."""
        safe_entities = []

        entities = context_result.get('entities', [])
        for entity in entities:
            # Consider entities safe if they have low change risk and few dependencies
            if (entity.get('change_risk') == 'low' and
                len(entity.get('used_by', [])) < 5 and
                entity.get('criticality') in ['low', 'medium']):

                safe_entities.append({
                    "entity_name": entity.get('entity_name', 'Unknown'),
                    "entity_type": entity.get('entity_type', 'Unknown'),
                    "file_path": entity.get('file_path', 'Unknown'),
                    "module_name": entity.get('module_name', 'Unknown'),
                    "criticality": entity.get('criticality', 'unknown'),
                    "change_risk": entity.get('change_risk', 'unknown'),
                    "used_by_count": len(entity.get('used_by', [])),
                    "relevance_score": entity.get('relevance_score', 0.0),
                    "why_safe": self._explain_safety(entity),
                    "integration_suggestion": self._suggest_integration(entity)
                })

        # Sort by relevance score
        return sorted(safe_entities, key=lambda x: x["relevance_score"], reverse=True)

    def _extract_related_entities(self, context_result: dict) -> list:
        """Extract entities that are related but not critical or safe."""
        related_entities = []

        entities = context_result.get('entities', [])
        for entity in entities:
            # Skip entities that are already classified as critical or safe
            is_critical = (entity.get('criticality') == 'high' or
                          entity.get('change_risk') == 'high' or
                          len(entity.get('used_by', [])) > 10)

            is_safe = (entity.get('change_risk') == 'low' and
                      len(entity.get('used_by', [])) < 5 and
                      entity.get('criticality') in ['low', 'medium'])

            if not is_critical and not is_safe:
                related_entities.append({
                    "entity_name": entity.get('entity_name', 'Unknown'),
                    "entity_type": entity.get('entity_type', 'Unknown'),
                    "file_path": entity.get('file_path', 'Unknown'),
                    "module_name": entity.get('module_name', 'Unknown'),
                    "criticality": entity.get('criticality', 'unknown'),
                    "change_risk": entity.get('change_risk', 'unknown'),
                    "used_by_count": len(entity.get('used_by', [])),
                    "dependencies_count": len(entity.get('calls', [])),
                    "relevance_score": entity.get('relevance_score', 0.0),
                    "relationship_type": self._determine_relationship(entity)
                })

        # Sort by relevance score
        return sorted(related_entities, key=lambda x: x["relevance_score"], reverse=True)

    def _generate_guidance(self, analysis_results: dict, context_result: dict) -> str:
        """Generate implementation guidance from analysis results."""
        guidance_parts = []

        # Add analysis summary
        total_entities = context_result.get('total_entities', 0)
        selection_rationale = context_result.get('selection_rationale', 'No rationale available')
        guidance_parts.append(f"Analysis Summary: {selection_rationale}")

        # Add confidence-based recommendations
        overall_confidence = analysis_results.get('overall_confidence', 0.0)
        if overall_confidence > 0.8:
            guidance_parts.append("High confidence analysis - proceed with implementation.")
        elif overall_confidence > 0.6:
            guidance_parts.append("Medium confidence - consider additional analysis for complex areas.")
        else:
            guidance_parts.append("Low confidence - recommend deeper investigation before implementation.")

        # Add entity-based guidance
        if total_entities > 30:
            guidance_parts.append("Large number of entities selected - consider breaking feature into smaller parts.")
        elif total_entities < 5:
            guidance_parts.append("Few entities selected - verify feature scope is comprehensive.")
        else:
            guidance_parts.append("Moderate entity count - good scope for feature implementation.")

        # Add iteration-based insights if available
        if 'iteration_history' in analysis_results and analysis_results['iteration_history']:
            final_iteration = analysis_results['iteration_history'][-1]
            status = final_iteration.get('status', 'unknown')
            guidance_parts.append(f"Multi-turn analysis status: {status}")

        return "\n".join(guidance_parts)

    def _generate_recommendations(self, analysis_results: dict, context_result: dict) -> list:
        """Generate actionable recommendations."""
        recommendations = []

        # Extract global insights from multi-turn analysis
        global_insights = analysis_results.get('global_insights', [])
        for insight in global_insights[:3]:  # Top 3 insights
            if isinstance(insight, str) and len(insight.strip()) > 0:
                recommendations.append(insight.strip())

        # Add confidence-based recommendations
        overall_confidence = analysis_results.get('overall_confidence', 0.0)
        if overall_confidence < 0.7:
            recommendations.append("Consider running additional analysis iterations for better confidence")

        # Add entity-specific recommendations
        total_entities = context_result.get('total_entities', 0)
        if total_entities > 30:
            recommendations.append("Large number of entities selected - consider breaking feature into smaller parts")
        elif total_entities < 5:
            recommendations.append("Few entities selected - verify feature scope is comprehensive")

        # Add quality-based recommendations
        quality_metrics = context_result.get('quality_metrics', {})
        token_utilization = quality_metrics.get('token_utilization', 0)
        if token_utilization < 50:
            recommendations.append("Low token utilization - consider expanding analysis scope")
        elif token_utilization > 95:
            recommendations.append("High token utilization - consider reducing scope or increasing token budget")

        # Add general best practices
        recommendations.extend([
            "Start with safe integration points to minimize risk",
            "Test critical entities thoroughly before modification",
            "Document changes to maintain code clarity"
        ])

        return recommendations[:10]  # Limit to top 10 recommendations

    def _extract_dependency_map(self, context_result: dict) -> dict:
        """Extract and format dependency relationships."""
        dependency_map = {}

        entities = context_result.get('entities', [])
        for entity in entities:
            entity_name = entity.get('entity_name', 'Unknown')
            calls = entity.get('calls', [])
            used_by = entity.get('used_by', [])

            if calls or used_by:
                dependency_map[entity_name] = {
                    'calls': calls[:10],  # Limit to first 10 for readability
                    'used_by': used_by[:10],  # Limit to first 10 for readability
                    'file_path': entity.get('file_path', 'Unknown')
                }

        return dependency_map

    def _explain_risk(self, entity: dict) -> str:
        """Explain why an entity is high risk."""
        reasons = []

        criticality = entity.get('criticality', 'unknown')
        change_risk = entity.get('change_risk', 'unknown')
        used_by_count = len(entity.get('used_by', []))
        side_effects = entity.get('side_effects', [])

        if criticality == 'high':
            reasons.append("High criticality component")

        if used_by_count > 10:
            reasons.append(f"Used by {used_by_count} other components")
        elif used_by_count > 5:
            reasons.append(f"Used by {used_by_count} components")

        if change_risk == 'high':
            reasons.append("High change risk assessment")

        if side_effects and 'none' not in side_effects:
            reasons.append("Has side effects")

        return "; ".join(reasons) if reasons else "Risk factors detected"

    def _explain_safety(self, entity: dict) -> str:
        """Explain why an entity is safe to modify."""
        reasons = []

        change_risk = entity.get('change_risk', 'unknown')
        used_by_count = len(entity.get('used_by', []))
        entity_type = entity.get('entity_type', 'unknown')
        criticality = entity.get('criticality', 'unknown')

        if change_risk == 'low':
            reasons.append("Low change risk")

        if used_by_count < 3:
            reasons.append("Few dependencies")
        elif used_by_count < 5:
            reasons.append("Limited dependencies")

        if entity_type in ['variable', 'constant']:
            reasons.append("Simple entity type")

        if criticality == 'low':
            reasons.append("Low criticality")

        return "; ".join(reasons) if reasons else "Low risk factors"

    def _suggest_integration(self, entity: dict) -> str:
        """Suggest how to integrate with this entity."""
        entity_type = entity.get('entity_type', 'unknown')
        entity_name = entity.get('entity_name', 'Unknown')

        if entity_type == 'function':
            return f"Consider extending or calling {entity_name}"
        elif entity_type == 'class':
            return f"Consider inheriting from or composing with {entity_name}"
        elif entity_type == 'variable':
            return f"Consider using or extending {entity_name}"
        elif entity_type == 'constant':
            return f"Consider referencing {entity_name}"
        else:
            return "Safe integration point identified"

    def _determine_relationship(self, entity: dict) -> str:
        """Determine the relationship type of an entity."""
        calls_count = len(entity.get('calls', []))
        used_by_count = len(entity.get('used_by', []))

        if calls_count > used_by_count * 2:
            return "Consumer (calls many functions)"
        elif used_by_count > calls_count * 2:
            return "Provider (used by many functions)"
        elif used_by_count > 5:
            return "Hub (central component)"
        elif calls_count > 5:
            return "Orchestrator (coordinates many calls)"
        else:
            return "Balanced (moderate dependencies)"

    def get_files_that_import(self, project_path: str, target_file_path: str, model_name: str = "gpt-3.5-turbo") -> List[str]:
        """
        Get a list of files that import (reference) the target file.

        Args:
            project_path: Path to the project root
            target_file_path: Path to the target file
            model_name: Name of the model to use

        Returns:
            A list of file paths that import the target file
        """
        return self.project_manager.get_files_that_import(project_path, model_name, target_file_path)

    def get_files_imported_by(self, project_path: str, target_file_path: str, model_name: str = "gpt-3.5-turbo") -> List[str]:
        """
        Get a list of files that are imported by the target file.

        Args:
            project_path: Path to the project root
            target_file_path: Path to the target file
            model_name: Name of the model to use

        Returns:
            A list of file paths that are imported by the target file
        """
        return self.project_manager.get_files_imported_by(project_path, model_name, target_file_path)

    def get_base_classes_of(self, project_path: str, class_name: str, file_path: str = None, model_name: str = "gpt-3.5-turbo") -> List[Dict]:
        """
        Get the base classes of the specified class.

        Args:
            project_path: Path to the project root
            class_name: Name of the class to analyze
            file_path: Path to the file containing the class (optional)
            model_name: Name of the model to use

        Returns:
            A list of dictionaries with base class information
        """
        return self.project_manager.get_base_classes_of(project_path, model_name, class_name, file_path)

    def get_derived_classes_of(self, project_path: str, class_name: str, file_path: str = None, model_name: str = "gpt-3.5-turbo") -> List[Dict]:
        """
        Get the derived classes of the specified class.

        Args:
            project_path: Path to the project root
            class_name: Name of the class to analyze
            file_path: Path to the file containing the class (optional)
            model_name: Name of the model to use

        Returns:
            A list of dictionaries with derived class information
        """
        return self.project_manager.get_derived_classes_of(project_path, model_name, class_name, file_path)

    def get_top_central_files(self, project_path: str, count: int = 10, model_name: str = "gpt-3.5-turbo") -> List[Dict]:
        """
        Get the top central files in the project based on reference count.

        Args:
            project_path: Path to the project root
            count: Number of top files to return
            model_name: Name of the model to use

        Returns:
            A list of dictionaries with file paths and reference counts
        """
        return self.project_manager.get_top_central_files(project_path, model_name, count)

    def get_symbols_defined_in_file(self, project_path: str, file_path: str, model_name: str = "gpt-3.5-turbo") -> Dict[str, List[str]]:
        """
        Get all symbols (functions, classes, variables) defined in a file.

        Args:
            project_path: Path to the project root
            file_path: Path to the file to analyze
            model_name: Name of the model to use

        Returns:
            Dictionary with symbol types as keys and lists of symbol names as values
        """
        return self.project_manager.get_symbols_defined_in_file(project_path, model_name, file_path)

    def find_file_defining_symbol(self, project_path: str, symbol_name: str, model_name: str = "gpt-3.5-turbo") -> Optional[str]:
        """
        Find the file that defines a specific symbol.

        Args:
            project_path: Path to the project root
            symbol_name: Name of the symbol to find
            model_name: Name of the model to use

        Returns:
            Relative path to the file defining the symbol, or None if not found
        """
        return self.project_manager._find_file_defining_symbol(project_path, model_name, symbol_name)

    def clear_cache(self) -> None:
        """
        Clear all cached data.
        """
        self.project_manager.class_inheritance_cache.clear()
        self.project_manager.symbol_cache.clear()
        self.project_manager.cache_timestamps.clear()
        if self.context_extractor:
            self.context_extractor.context_cache.clear()
            self.context_extractor.cache_timestamps.clear()

    def set_cache_ttl(self, ttl: int) -> None:
        """
        Set the time-to-live for cache entries.

        Args:
            ttl: Time-to-live in seconds
        """
        self.project_manager.cache_ttl = ttl
        if self.context_extractor:
            self.context_extractor.cache_ttl = ttl

    def _get_context_extractor(self):
        """
        Get the surgical context extractor, initializing it if necessary.

        Returns:
            The SurgicalContextExtractor instance
        """
        if self.context_extractor is None:
            # Import here to avoid circular imports
            from surgical_context_extractor import SurgicalContextExtractor
            self.context_extractor = SurgicalContextExtractor(self)
        return self.context_extractor

    def get_contextual_dependencies(self, project_path: str, primary_file: str,
                                   max_snippets: int = 20) -> Dict:
        """
        Get a comprehensive map of contextual dependencies for a file.

        Args:
            project_path: Path to the project root
            primary_file: Path to the primary file to analyze
            max_snippets: Maximum number of snippets to include

        Returns:
            A dictionary with contextual dependency information
        """
        extractor = self._get_context_extractor()
        context_map = extractor.get_contextual_dependencies(project_path, primary_file, max_snippets)

        # Convert to a serializable dictionary
        result = {
            'primary_file': context_map.primary_file,
            'related_files': context_map.related_files,
            'usage_contexts': [],
            'definition_contexts': []
        }

        # Add usage contexts
        for usage_ctx in context_map.usage_contexts:
            result['usage_contexts'].append({
                'file_path': usage_ctx.snippet.file_path,
                'start_line': usage_ctx.snippet.start_line,
                'end_line': usage_ctx.snippet.end_line,
                'content': usage_ctx.snippet.content,
                'symbol_name': usage_ctx.snippet.symbol_name,
                'usage_type': usage_ctx.usage_type.value,
                'surrounding_function': usage_ctx.snippet.surrounding_function
            })

        # Add definition contexts
        for def_ctx in context_map.definition_contexts:
            result['definition_contexts'].append({
                'file_path': def_ctx.snippet.file_path,
                'start_line': def_ctx.snippet.start_line,
                'end_line': def_ctx.snippet.end_line,
                'content': def_ctx.snippet.content,
                'symbol_name': def_ctx.snippet.symbol_name,
                'definition_type': def_ctx.definition_type.value,
                'signature': def_ctx.signature,
                'docstring': def_ctx.docstring
            })

        return result

    def get_focused_inheritance_context(self, project_path: str, class_name: str,
                                      file_path: str) -> Dict:
        """
        Get focused context around class inheritance relationships.

        Args:
            project_path: Path to the project root
            class_name: Name of the class to analyze
            file_path: Path to the file containing the class

        Returns:
            A dictionary with inheritance context information
        """
        extractor = self._get_context_extractor()
        inheritance_map = extractor.get_focused_inheritance_context(project_path, class_name, file_path)

        # Convert to a serializable dictionary
        result = {
            'class_name': inheritance_map.class_name,
            'file_path': inheritance_map.file_path,
            'base_classes': [],
            'derived_classes': [],
            'implementation_contexts': []
        }

        # Add base classes
        for base_ctx in inheritance_map.base_classes:
            result['base_classes'].append({
                'file_path': base_ctx.snippet.file_path,
                'start_line': base_ctx.snippet.start_line,
                'end_line': base_ctx.snippet.end_line,
                'content': base_ctx.snippet.content,
                'symbol_name': base_ctx.snippet.symbol_name,
                'signature': base_ctx.signature,
                'docstring': base_ctx.docstring
            })

        # Add derived classes
        for derived_ctx in inheritance_map.derived_classes:
            result['derived_classes'].append({
                'file_path': derived_ctx.snippet.file_path,
                'start_line': derived_ctx.snippet.start_line,
                'end_line': derived_ctx.snippet.end_line,
                'content': derived_ctx.snippet.content,
                'symbol_name': derived_ctx.snippet.symbol_name,
                'signature': derived_ctx.signature,
                'docstring': derived_ctx.docstring
            })

        # Add implementation contexts
        for impl_ctx in inheritance_map.implementation_contexts:
            result['implementation_contexts'].append({
                'file_path': impl_ctx.file_path,
                'start_line': impl_ctx.start_line,
                'end_line': impl_ctx.end_line,
                'content': impl_ctx.content,
                'symbol_name': impl_ctx.symbol_name,
                'surrounding_function': impl_ctx.surrounding_function
            })

        return result

    def get_symbol_references_between_files(self, project_path: str, source_file: str, target_file: str, model_name: str = "gpt-3.5-turbo") -> Dict[str, List[str]]:
        """
        Get detailed information about symbols referenced between two files.

        Args:
            project_path: Path to the project root
            source_file: Path to the source file that references symbols
            target_file: Path to the target file that defines symbols
            model_name: Name of the model to use

        Returns:
            Dictionary with symbol types as keys and lists of referenced symbol names as values
        """
        # Get symbols defined in the target file
        target_symbols = self.get_symbols_defined_in_file(project_path, target_file, model_name)

        # Get all symbols in the source file
        repo_map = self.project_manager._get_repo_map(project_path, model_name)
        if not repo_map:
            return {'functions': [], 'classes': [], 'variables': []}

        try:
            # Normalize file paths
            source_file = self.project_manager._normalize_path(source_file)
            target_file = self.project_manager._normalize_path(target_file)

            source_rel_path = os.path.relpath(source_file, project_path)
            target_rel_path = os.path.relpath(target_file, project_path)

            # Get tags for the source file
            source_tags = list(repo_map.get_tags(source_file, source_rel_path))

            # Find references to symbols defined in the target file
            referenced_symbols = {
                'functions': [],
                'classes': [],
                'variables': [],
                'imports': []
            }

            # Check for direct imports
            for tag in source_tags:
                if tag.kind == 'import':
                    # Check if this import references the target file
                    target_module = self.project_manager._get_module_path(target_rel_path)
                    if target_module in tag.name:
                        referenced_symbols['imports'].append(tag.name)

            # Check for references to symbols defined in the target file
            for tag in source_tags:
                if tag.kind == 'ref':
                    # Check if this reference is to a symbol defined in the target file
                    if tag.name in target_symbols['functions']:
                        if tag.name not in referenced_symbols['functions']:
                            referenced_symbols['functions'].append(tag.name)
                    elif tag.name in target_symbols['classes']:
                        if tag.name not in referenced_symbols['classes']:
                            referenced_symbols['classes'].append(tag.name)
                    elif tag.name in target_symbols['variables']:
                        if tag.name not in referenced_symbols['variables']:
                            referenced_symbols['variables'].append(tag.name)

            # If we have access to the source file, try to extract more detailed information
            try:
                abs_path = os.path.abspath(source_file)
                if os.path.exists(abs_path):
                    with open(abs_path, 'r', encoding='utf-8') as f:
                        file_content = f.read()

                    # Check for imports from the target file
                    target_basename = os.path.basename(target_file).replace('.py', '')
                    target_dirname = os.path.basename(os.path.dirname(target_file))

                    import_patterns = [
                        rf'from\s+{target_dirname}\.{target_basename}\s+import\s+(.+)',
                        rf'from\s+{target_basename}\s+import\s+(.+)',
                        rf'import\s+{target_dirname}\.{target_basename}',
                        rf'import\s+{target_basename}'
                    ]

                    for pattern in import_patterns:
                        for line in file_content.split('\n'):
                            match = re.search(pattern, line.strip())
                            if match and match.group(1):
                                imports = [imp.strip() for imp in match.group(1).split(',')]
                                for imp in imports:
                                    if imp in target_symbols['functions'] and imp not in referenced_symbols['functions']:
                                        referenced_symbols['functions'].append(imp)
                                    elif imp in target_symbols['classes'] and imp not in referenced_symbols['classes']:
                                        referenced_symbols['classes'].append(imp)
                                    elif imp in target_symbols['variables'] and imp not in referenced_symbols['variables']:
                                        referenced_symbols['variables'].append(imp)
            except Exception as e:
                print(f"Error extracting additional references from file: {e}")

            return referenced_symbols
        except Exception as e:
            print(f"Error getting symbol references between files: {e}")
            return {'functions': [], 'classes': [], 'variables': [], 'imports': []}


if __name__ == "__main__":
    # Example usage
    service = AiderIntegrationService()
    project_path = "aider-main"

    print("\n=== DEPENDENCY ANALYSIS DEMO ===")
    print("\n1. Basic File Dependencies")

    # Get top central files
    top_files = service.get_top_central_files(project_path, count=10)
    print("\nTop Central Files:")
    for file in top_files:
        print(f"- {file['file']}: referenced by {file['references']} files")

    # Get files that import a specific file
    target_file = "aider-main/aider/repomap.py"
    importing_files = service.get_files_that_import(project_path, target_file)
    print(f"\nFiles that import {target_file}:")
    for file in importing_files:
        print(f"- {file}")

    # Get files imported by a specific file
    imported_files = service.get_files_imported_by(project_path, target_file)
    print(f"\nFiles imported by {target_file}:")
    for file in imported_files:
        print(f"- {file}")

    print("\n2. Class Inheritance Analysis")

    # Get base classes of a specific class
    class_name = "RepoMap"
    base_classes = service.get_base_classes_of(project_path, class_name)
    print(f"\nBase classes of {class_name}:")
    if base_classes:
        for base_class in base_classes:
            print(f"- {base_class['class_name']} in {base_class['file_path']} ({base_class['module_path']})")
    else:
        print("- No base classes found")

    # Get derived classes of a specific class
    class_name = "Coder"
    derived_classes = service.get_derived_classes_of(project_path, class_name)
    print(f"\nDerived classes of {class_name}:")
    for derived_class in derived_classes:
        print(f"- {derived_class['class_name']} in {derived_class['file_path']} ({derived_class['module_path']})")

    print("\n3. Symbol Analysis")

    # Get symbols defined in a file
    file_path = "aider-main/aider/repomap.py"
    symbols = service.get_symbols_defined_in_file(project_path, file_path)
    print(f"\nSymbols defined in {file_path}:")
    for symbol_type, symbol_list in symbols.items():
        if symbol_list:
            print(f"  {symbol_type.capitalize()}:")
            for symbol in symbol_list[:5]:  # Show only first 5 symbols of each type
                print(f"    - {symbol}")
            if len(symbol_list) > 5:
                print(f"    - ... and {len(symbol_list) - 5} more")

    # Find file defining a symbol
    symbol_name = "RepoMap"
    defining_file = service.find_file_defining_symbol(project_path, symbol_name)
    print(f"\nFile defining symbol '{symbol_name}':")
    print(f"- {defining_file if defining_file else 'Not found'}")

    print("\n4. Detailed Symbol References (New Feature)")

    # Get symbol references between files
    source_file = "aider-main/aider/coders/base_coder.py"
    target_file = "aider-main/aider/repomap.py"

    print(f"\nSymbols from {target_file} referenced in {source_file}:")
    symbol_refs = service.get_symbol_references_between_files(project_path, source_file, target_file)

    for symbol_type, symbol_list in symbol_refs.items():
        if symbol_list:
            print(f"  {symbol_type.capitalize()}:")
            for symbol in symbol_list:
                print(f"    - {symbol}")

    print("\n5. Surgical Context Extraction (New Feature)")

    # Get contextual dependencies for a file
    primary_file = "aider-main/aider/coders/base_coder.py"
    print(f"\nContextual dependencies for {primary_file}:")

    try:
        context_map = service.get_contextual_dependencies(project_path, primary_file, max_snippets=6)

        print(f"\nPrimary file: {context_map['primary_file']}")
        print(f"Related files: {', '.join(context_map['related_files'][:3]) if context_map['related_files'] else 'None'}...")

        # Show usage contexts
        print("\nUsage contexts:")
        for i, usage_ctx in enumerate(context_map['usage_contexts'][:2]):  # Show only first 2
            print(f"\n  {i+1}. {usage_ctx['symbol_name']} ({usage_ctx['usage_type']})")
            print(f"     File: {usage_ctx['file_path']}, Lines: {usage_ctx['start_line']}-{usage_ctx['end_line']}")
            print(f"     Surrounding function: {usage_ctx['surrounding_function'] or 'N/A'}")

            # Show a snippet of the content (first 3 lines)
            content_lines = usage_ctx['content'].split('\n')
            snippet = '\n'.join(content_lines[:3])
            if len(content_lines) > 3:
                snippet += '\n...'
            print(f"     Snippet:\n{snippet}")

        # Show definition contexts
        print("\nDefinition contexts:")
        for i, def_ctx in enumerate(context_map['definition_contexts'][:2]):  # Show only first 2
            print(f"\n  {i+1}. {def_ctx['symbol_name']} ({def_ctx['definition_type']})")
            print(f"     File: {def_ctx['file_path']}, Lines: {def_ctx['start_line']}-{def_ctx['end_line']}")
            if def_ctx['signature']:
                print(f"     Signature: {def_ctx['signature'][:50]}...")

            # Show a snippet of the content (first 3 lines)
            content_lines = def_ctx['content'].split('\n')
            snippet = '\n'.join(content_lines[:3])
            if len(content_lines) > 3:
                snippet += '\n...'
            print(f"     Snippet:\n{snippet}")

    except Exception as e:
        print(f"Error demonstrating contextual dependencies: {e}")

    # Get focused inheritance context
    class_name = "Coder"
    file_path = "aider-main/aider/coders/base_coder.py"
    print(f"\nFocused inheritance context for {class_name}:")

    try:
        inheritance_ctx = service.get_focused_inheritance_context(project_path, class_name, file_path)

        # Show base classes
        print("\nBase classes:")
        for i, base_ctx in enumerate(inheritance_ctx['base_classes'][:2]):  # Show only first 2
            print(f"\n  {i+1}. {base_ctx['symbol_name']}")
            print(f"     File: {base_ctx['file_path']}, Lines: {base_ctx['start_line']}-{base_ctx['end_line']}")
            if base_ctx['signature']:
                print(f"     Signature: {base_ctx['signature'][:50]}...")

            # Show a snippet of the content (first 3 lines)
            content_lines = base_ctx['content'].split('\n')
            snippet = '\n'.join(content_lines[:3])
            if len(content_lines) > 3:
                snippet += '\n...'
            print(f"     Snippet:\n{snippet}")

        # Show derived classes
        print("\nDerived classes:")
        for i, derived_ctx in enumerate(inheritance_ctx['derived_classes'][:2]):  # Show only first 2
            print(f"\n  {i+1}. {derived_ctx['symbol_name']}")
            print(f"     File: {derived_ctx['file_path']}, Lines: {derived_ctx['start_line']}-{derived_ctx['end_line']}")

            # Show a snippet of the content (first 3 lines)
            content_lines = derived_ctx['content'].split('\n')
            snippet = '\n'.join(content_lines[:3])
            if len(content_lines) > 3:
                snippet += '\n...'
            print(f"     Snippet:\n{snippet}")

    except Exception as e:
        print(f"Error demonstrating inheritance context: {e}")

    print("\nSurgical Context Extraction successfully implemented!")

    print("\n6. Intelligent Context Selection (Phase 2 Feature)")

    # Test the new intelligent context selection
    print("\nTesting Intelligent Context Selection...")

    try:
        # Test different task scenarios
        test_scenarios = [
            {
                'task': 'Fix a bug in the file processing logic',
                'type': 'debugging',
                'focus': ['file', 'process']
            },
            {
                'task': 'Add a new feature for dependency analysis',
                'type': 'feature_development',
                'focus': ['dependency', 'analysis']
            }
        ]

        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n📋 Scenario {i}: {scenario['task']}")

            context_result = service.select_intelligent_context(
                project_path=project_path,
                task_description=scenario['task'],
                task_type=scenario['type'],
                focus_entities=scenario['focus'],
                max_tokens=2000  # Smaller budget for demo
            )

            if 'error' not in context_result:
                print(f"✅ Context selected successfully!")
                print(f"   Selected entities: {context_result['total_entities']}")
                print(f"   Token utilization: {context_result['quality_metrics']['token_utilization']:.1f}%")
                print(f"   Average relevance: {context_result['quality_metrics']['average_relevance_score']:.2f}")

                # Show top 3 entities
                top_entities = context_result['entities'][:3]
                print(f"   Top entities:")
                for j, entity in enumerate(top_entities, 1):
                    print(f"     {j}. {entity['module_name']}.{entity['entity_name']} "
                          f"(score: {entity['relevance_score']:.2f})")
            else:
                print(f"⚠️ Context selection failed: {context_result['error']}")

        print("\n✅ Intelligent Context Selection successfully demonstrated!")

    except Exception as e:
        print(f"⚠️ Intelligent Context Selection test failed: {e}")

    print("\n7. Mid-Level IR Generation (Foundation Feature)")

    # Generate Mid-Level IR for a subset of files (to avoid overwhelming output)
    print("\nGenerating Mid-Level IR for the project...")

    try:
        # Generate the complete IR
        ir_data = service.generate_mid_level_ir(project_path)

        print(f"\n✅ Mid-Level IR generated successfully!")
        print(f"   Total modules analyzed: {ir_data['metadata']['total_modules']}")
        print(f"   Generated at: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(ir_data['metadata']['generated_at']))}")

        # Show a sample of the IR structure
        print("\n📋 Sample IR Structure (first 2 modules):")

        sample_modules = ir_data['modules'][:2]  # Show first 2 modules

        for i, module in enumerate(sample_modules, 1):
            print(f"\n  Module {i}: {module['name']}")
            print(f"    File: {module['file']}")
            print(f"    Lines of Code: {module['loc']}")
            print(f"    Dependencies: {len(module['dependencies'])}")
            print(f"    Entities: {len(module['entities'])}")

            # Show dependencies
            if module['dependencies']:
                print("    Dependencies:")
                for dep in module['dependencies'][:3]:  # Show first 3 dependencies
                    print(f"      - {dep['module']} (strength: {dep['strength']})")
                if len(module['dependencies']) > 3:
                    print(f"      - ... and {len(module['dependencies']) - 3} more")

            # Show entities
            if module['entities']:
                print("    Entities:")
                for entity in module['entities'][:2]:  # Show first 2 entities
                    print(f"      - {entity['type']}: {entity['name']}")
                    print(f"        Doc: {entity['doc'][:50]}...")
                    print(f"        Params: {entity['params']}")
                    print(f"        Returns: {entity['returns']}")
                    print(f"        Calls: {entity['calls'][:3]}")  # Show first 3 calls
                    print(f"        Side effects: {entity['side_effects']}")
                    print(f"        Criticality: {entity['criticality']}")
                    print(f"        Change risk: {entity['change_risk']}")
                if len(module['entities']) > 2:
                    print(f"      - ... and {len(module['entities']) - 2} more entities")

        # Save the IR to a file for inspection
        output_file = "mid_level_ir_output.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(ir_data, f, indent=2, ensure_ascii=False)

        print(f"\n💾 Complete Mid-Level IR saved to: {output_file}")
        print("   You can inspect the full structure in this file.")

        # Show some statistics
        total_entities = sum(len(module['entities']) for module in ir_data['modules'])
        total_dependencies = sum(len(module['dependencies']) for module in ir_data['modules'])

        print(f"\n📊 IR Statistics:")
        print(f"   Total modules: {len(ir_data['modules'])}")
        print(f"   Total entities: {total_entities}")
        print(f"   Total dependencies: {total_dependencies}")

        # Show criticality distribution
        criticality_counts = {'high': 0, 'medium': 0, 'low': 0}
        for module in ir_data['modules']:
            for entity in module['entities']:
                criticality = entity.get('criticality', 'medium')
                criticality_counts[criticality] = criticality_counts.get(criticality, 0) + 1

        print(f"   Criticality distribution:")
        print(f"     High: {criticality_counts['high']}")
        print(f"     Medium: {criticality_counts['medium']}")
        print(f"     Low: {criticality_counts['low']}")

    except Exception as e:
        print(f"❌ Error generating Mid-Level IR: {e}")
        import traceback
        traceback.print_exc()

    print("\n7. Cache Management")
    print("\nClearing cache...")
    service.clear_cache()
    print("Cache cleared successfully.")

    print("\nSetting cache TTL to 1 hour...")
    service.set_cache_ttl(3600)
    print("Cache TTL set successfully.")
